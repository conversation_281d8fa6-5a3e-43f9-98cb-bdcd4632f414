package com.les.its.scm.busdata;

import com.alibaba.fastjson.JSONObject;
import com.les.ads.common.util.HttpUtil;
import com.les.ads.ds.ReturnEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Project: nacos-server-parent
 * @Package: com.les.its.signalspecialcontrol.service
 * @Author： mujian
 * @Create： 2022/11/8 18:17
 * @Description： TODO
 * @History: modify
 */
@Slf4j
@Service
public class BusGPSService {

    @Value("${scc.url:}")
    private String sccUrl;

    @Value("${ssc.url:}")
    private String sscUrl;


    @Async
    public void sendBusGPS(GpsData gpsData){
        try {
            ReturnEntity rsp = HttpUtil.post(sccUrl + "/core/v1.0/gpsData", gpsData, ReturnEntity.class);

        } catch (Exception e) {
            log.error("sendBusGPS message:{}",JSONObject.toJSONString(gpsData));

        }
    }

    @Async
    public void sendBusGPSToSSC(GpsData gpsData){
        try {
            ReturnEntity rsp = HttpUtil.post(sscUrl + "/busGps/save", gpsData, ReturnEntity.class);

        } catch (Exception e) {
            log.error("sendBusGPS message:{}",JSONObject.toJSONString(gpsData));

        }
    }
}
