//
//package com.les.its.scm.busdata;
//
//import com.les.its.scm.busdata.himalaya.proto.BsmDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.integration.annotation.ServiceActivator;
//import org.springframework.integration.channel.DirectChannel;
//import org.springframework.integration.core.MessageProducer;
//import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
//import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
//import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
//import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
//import org.springframework.messaging.MessageChannel;
//import org.springframework.messaging.MessageHandler;
//
//@Slf4j
//@Configuration
//@ConditionalOnProperty(prefix = "mqtt", name = "serverURIs")
//public class MqttProtoConfig {
//
//    @Value("${mqtt.subTopics}")
//    private String subTopics;
//
//    @Value("${mqtt.username}")
//    private String username;
//
//    @Value("${mqtt.password}")
//    private String password;
//
//    @Value("${mqtt.serverURIs}")
//    private String hostUrl;
//
//    @Value("${mqtt.client.id}")
//    private String clientId;
//
//    @Bean
//    public MqttPahoClientFactory mqttClientFactory() {
//        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
//        MqttConnectOptions options = new MqttConnectOptions();
//        options.setServerURIs(new String[]{hostUrl});
//        options.setUserName(username);
//        options.setPassword(password.toCharArray());
//        options.setCleanSession(true);
//        options.setConnectionTimeout(30);
//        options.setKeepAliveInterval(60);
//        factory.setConnectionOptions(options);
//        return factory;
//    }
//
//    @Bean
//    public MessageChannel mqttInputChannel() {
//        return new DirectChannel();
//    }
//
//    @Bean
//    public MessageProducer mqttInbound() {
//        MqttPahoMessageDrivenChannelAdapter adapter =
//                new MqttPahoMessageDrivenChannelAdapter(
//                        clientId + "_inbound", mqttClientFactory(), subTopics.split(","));
//        adapter.setCompletionTimeout(5000);
//        adapter.setConverter(new DefaultPahoMessageConverter());
//        adapter.setQos(1);
//        adapter.setOutputChannel(mqttInputChannel());
//        return adapter;
//    }
//
//    @Bean
//    @ServiceActivator(inputChannel = "mqttInputChannel")
//    public MessageHandler mqttMessageHandler() {
//        return message -> {
//            try {
//                Object payload = message.getPayload();
//
//                // 处理字节数组形式的 protobuf 数据
//                if (payload instanceof byte[]) {
//                    byte[] data = (byte[]) payload;
//                    processProtoData(data);
//                }
//                // 处理字符串形式的数据（兼容性考虑）
//                else if (payload instanceof String) {
//                    String data = (String) payload;
//                    // 如果是 base64 编码的 protobuf 数据
//                    try {
//                        byte[] decoded = java.util.Base64.getDecoder().decode(data);
//                        processProtoData(decoded);
//                    } catch (Exception e) {
//                        log.debug("数据不是 base64 编码格式");
//                    }
//                }
//                else {
//                    log.warn("未知的数据格式: {}", payload.getClass().getName());
//                }
//            } catch (Exception e) {
//                log.error("处理 MQTT 消息时出错", e);
//            }
//        };
//    }
//
//    private void processProtoData(byte[] data) {
//        try {
//            if (data == null || data.length == 0) {
//                log.warn("接收到空的 protobuf 数据");
//                return;
//            }
//
//            // 解析 protobuf 数据
//            BsmDTO.VehData bsmData = BsmDTO.VehData.parseFrom(data);
//
//            // 处理解析后的数据
//            handleBsmData(bsmData);
//
//        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
//            log.error("Protobuf 数据解析失败，数据长度: {}", data.length, e);
//        } catch (Exception e) {
//            log.error("处理 protobuf 数据时出错", e);
//        }
//    }
//
//    private void handleBsmData(BsmDTO bsmData) {
//        try {
//            log.debug("接收到车辆 {} 的数据", bsmData.getVehId());
//
//            // 根据您的业务需求处理数据
//            // 示例：打印部分字段
//            log.debug("车辆ID: {}", bsmData.getVehId());
//            log.debug("车牌号: {}", bsmData.getPlateNo());
//            log.debug("经度: {}", bsmData.getLongitude());
//            log.debug("纬度: {}", bsmData.getLatitude());
//            log.debug("高程: {}", bsmData.getElevation());
//            log.debug("航向角: {}", bsmData.getHeading());
//            log.debug("档位: {}", bsmData.getTapPos());
//
//            // 在这里添加您的业务逻辑
//            // 例如保存到数据库、转发到其他服务等
//
//        } catch (Exception e) {
//            log.error("处理 BsmDTO 数据时出错", e);
//        }
//    }
//}