package com.les.its.scm.busdata;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Project: nacos-server-parent
 * @Package: com.les.its.signalspecialcontrol.model
 * @Author： mujian
 * @Create： 2022/11/8 18:43
 * @Description： TODO
 * @History: modify
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "GPS数据")
public class GpsData implements Serializable {

    @Schema(description = "表号，201", type = "Integer")
    private Integer noTab;

    @Schema(description = "设备编号", type = "String")
    private String imei;

    @Schema(description = "经度 保留到小数点后六位", type = "Double")
    private Double lng;

    @Schema(description = "纬度 保留到小数点后六位", type = "Double")
    private Double lat;

    @Schema(description = "速度，km/h", type = "float")
    private Float speed;

    @Schema(description = "航向", type = "float")
    private Float bear;

    @Schema(description = "GPS状态 0正常，1基站定位，2无定位", type = "Integer")
    private Integer gpsState;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "GPS更新时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime date;
}
