package com.les.its.scm.busdata;

import com.les.ads.common.util.HttpUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@ComponentScan(basePackages = {"com.les", "com.myweb"})
@SpringBootApplication
public class BusDataZzApplication {

    public static void main(String[] args) {
        SpringApplication.run(BusDataZzApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder templateBuilder) {
        RestTemplate template = templateBuilder
                .setConnectTimeout(Duration.ofMillis(4000))
                .setReadTimeout(Duration.ofMillis(30000))
                .build();
        HttpUtil.configTemplate(template);
        return template;
    }

}
