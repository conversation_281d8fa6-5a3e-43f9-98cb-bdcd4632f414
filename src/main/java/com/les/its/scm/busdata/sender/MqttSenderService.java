package com.les.its.scm.busdata.sender;

import com.les.its.scm.busdata.himalaya.proto.BsmDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MqttSenderService {

    private final MessageChannel mqttOutboundChannel;


    public BsmDTO.VehData getBsmData() {
        BsmDTO.VehData build = BsmDTO.VehData
                .newBuilder().setVehId("999")
                .setPlateNo("苏A8888")
                .setLongitude(66.0000)
                .setLatitude(111.0000)
                .setSpeed(40.0).build();
        return build;
    }


    /**
     * 每30秒发送一批测试数据
     */
    @Scheduled(fixedRate = 5000)
    public void sendPeriodicTestData() {
        try {
            log.debug("定时发送测试数据...");

            // 构造主题
            String topic = "idse/tps-VehicleData-bsm/uuu";

            BsmDTO.VehData bsmData = getBsmData();

            sendSensorData(bsmData, topic);

            log.debug("定时发送完成，{}", bsmData);

        } catch (Exception e) {
            log.error("定时发送测试数据失败", e);
        }
    }

    /**
     * 发送Proto序列化的传感器数据
     * @param sensorData 传感器数据
     * @param topic MQTT主题
     */
    public void sendSensorData(BsmDTO.VehData sensorData, String topic) {
        try {

            // 序列化为字节数组
            byte[] payload = sensorData.toByteArray();

            // 构建消息
            Message<byte[]> message = MessageBuilder
                    .withPayload(payload)
                    .setHeader("mqtt_topic", topic)
                    .setHeader("mqtt_qos", 1)
                    .setHeader("mqtt_retained", false)
                    .build();

            // 发送消息
            boolean sent = mqttOutboundChannel.send(message);

            if (sent) {
                log.info("成功发送传感器数据到MQTT: topic={}, sensorData={}, payloadSize={}",
                        topic, sensorData, payload.length);
            } else {
                log.error("发送传感器数据到MQTT失败: topic={}, sensorData={}",
                        topic, sensorData);
            }

        } catch (Exception e) {
            log.error("发送传感器数据异常: topic={}, sensorData={}",
                    topic, sensorData, e);
            throw new RuntimeException("MQTT发送失败", e);
        }
    }


}