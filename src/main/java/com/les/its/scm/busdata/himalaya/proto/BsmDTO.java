// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: bsm.proto

package com.les.its.scm.busdata.himalaya.proto;

public final class BsmDTO {
  private BsmDTO() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface VehDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:proto.VehData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 车辆id
     * </pre>
     *
     * <code>string vehId = 1;</code>
     * @return The vehId.
     */
    java.lang.String getVehId();
    /**
     * <pre>
     * 车辆id
     * </pre>
     *
     * <code>string vehId = 1;</code>
     * @return The bytes for vehId.
     */
    com.google.protobuf.ByteString
        getVehIdBytes();

    /**
     * <pre>
     * 车牌
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     * @return The plateNo.
     */
    java.lang.String getPlateNo();
    /**
     * <pre>
     * 车牌
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     * @return The bytes for plateNo.
     */
    com.google.protobuf.ByteString
        getPlateNoBytes();

    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>double longitude = 3;</code>
     * @return The longitude.
     */
    double getLongitude();

    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>double latitude = 4;</code>
     * @return The latitude.
     */
    double getLatitude();

    /**
     * <pre>
     * 高程
     * </pre>
     *
     * <code>double elevation = 5;</code>
     * @return The elevation.
     */
    double getElevation();

    /**
     * <pre>
     *GNSS 时间戳
     * </pre>
     *
     * <code>int64 timestampGNSS = 6;</code>
     * @return The timestampGNSS.
     */
    long getTimestampGNSS();

    /**
     * <pre>
     *GNSS 速度
     * </pre>
     *
     * <code>double velocityGNSS = 7;</code>
     * @return The velocityGNSS.
     */
    double getVelocityGNSS();

    /**
     * <pre>
     *航向角
     * </pre>
     *
     * <code>int64 heading = 8;</code>
     * @return The heading.
     */
    long getHeading();

    /**
     * <pre>
     *  档位
     * </pre>
     *
     * <code>int32 tapPos = 9;</code>
     * @return The tapPos.
     */
    int getTapPos();

    /**
     * <pre>
     *  方向盘转角
     * </pre>
     *
     * <code>int32 steeringAngle = 10;</code>
     * @return The steeringAngle.
     */
    int getSteeringAngle();

    /**
     * <pre>
     *  当前车速
     * </pre>
     *
     * <code>double speed = 11;</code>
     * @return The speed.
     */
    double getSpeed();

    /**
     * <pre>
     *  纵向加速度
     * </pre>
     *
     * <code>double accelerationLon = 12;</code>
     * @return The accelerationLon.
     */
    double getAccelerationLon();

    /**
     * <pre>
     *  横向加速度
     * </pre>
     *
     * <code>double accelerationLat = 13;</code>
     * @return The accelerationLat.
     */
    double getAccelerationLat();

    /**
     * <pre>
     *  垂向加速度
     * </pre>
     *
     * <code>double accelerationVer = 14;</code>
     * @return The accelerationVer.
     */
    double getAccelerationVer();

    /**
     * <pre>
     *  横摆角速度
     * </pre>
     *
     * <code>double yawRate = 15;</code>
     * @return The yawRate.
     */
    double getYawRate();

    /**
     * <pre>
     * 油门开度
     * </pre>
     *
     * <code>double accelPos = 16;</code>
     * @return The accelPos.
     */
    double getAccelPos();

    /**
     * <pre>
     *   制动踏板开关
     * </pre>
     *
     * <code>int32 brakeFlag = 17;</code>
     * @return The brakeFlag.
     */
    int getBrakeFlag();

    /**
     * <pre>
     *  制动踏板开度
     * </pre>
     *
     * <code>double brakePos = 18;</code>
     * @return The brakePos.
     */
    double getBrakePos();

    /**
     * <pre>
     * 车辆驾驶模式
     * </pre>
     *
     * <code>int32 driveMode = 19;</code>
     * @return The driveMode.
     */
    int getDriveMode();

    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @return A list containing the tirePressure.
     */
    java.util.List<java.lang.Integer> getTirePressureList();
    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @return The count of tirePressure.
     */
    int getTirePressureCount();
    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @param index The index of the element to return.
     * @return The tirePressure at the given index.
     */
    int getTirePressure(int index);

    /**
     * <pre>
     * 安全气囊状态
     * </pre>
     *
     * <code>int32 airBag = 21;</code>
     * @return The airBag.
     */
    int getAirBag();

    /**
     * <pre>
     * 剩余油量
     * </pre>
     *
     * <code>int32 fuelGauge = 22;</code>
     * @return The fuelGauge.
     */
    int getFuelGauge();

    /**
     * <pre>
     * 剩余电池电量
     * </pre>
     *
     * <code>int32 soc = 23;</code>
     * @return The soc.
     */
    int getSoc();

    /**
     * <pre>
     * 电子手刹状态
     * </pre>
     *
     * <code>int32 epb = 24;</code>
     * @return The epb.
     */
    int getEpb();

    /**
     * <pre>
     * 前左轮速
     * </pre>
     *
     * <code>double wheelSpeedFL = 25;</code>
     * @return The wheelSpeedFL.
     */
    double getWheelSpeedFL();

    /**
     * <pre>
     * 前右轮速
     * </pre>
     *
     * <code>double wheelSpeedFR = 26;</code>
     * @return The wheelSpeedFR.
     */
    double getWheelSpeedFR();

    /**
     * <pre>
     * 后左轮速
     * </pre>
     *
     * <code>double wheelSpeedRL = 27;</code>
     * @return The wheelSpeedRL.
     */
    double getWheelSpeedRL();

    /**
     * <pre>
     * 后右轮速
     * </pre>
     *
     * <code>double wheelSpeedRR = 28;</code>
     * @return The wheelSpeedRR.
     */
    double getWheelSpeedRR();

    /**
     * <pre>
     * 电机转速
     * </pre>
     *
     * <code>double motorSpeed = 29;</code>
     * @return The motorSpeed.
     */
    double getMotorSpeed();

    /**
     * <pre>
     * 电机扭矩
     * </pre>
     *
     * <code>double motorTorque = 30;</code>
     * @return The motorTorque.
     */
    double getMotorTorque();

    /**
     * <pre>
     * G524 二进制数据，数据结构参考 planning
     * </pre>
     *
     * <code>bytes ext = 31;</code>
     * @return The ext.
     */
    com.google.protobuf.ByteString getExt();
  }
  /**
   * Protobuf type {@code proto.VehData}
   */
  public static final class VehData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:proto.VehData)
      VehDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use VehData.newBuilder() to construct.
    private VehData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private VehData() {
      vehId_ = "";
      plateNo_ = "";
      tirePressure_ = emptyIntList();
      ext_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new VehData();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.les.its.scm.busdata.himalaya.proto.BsmDTO.internal_static_proto_VehData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.les.its.scm.busdata.himalaya.proto.BsmDTO.internal_static_proto_VehData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.class, com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.Builder.class);
    }

    public static final int VEHID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object vehId_ = "";
    /**
     * <pre>
     * 车辆id
     * </pre>
     *
     * <code>string vehId = 1;</code>
     * @return The vehId.
     */
    @java.lang.Override
    public java.lang.String getVehId() {
      java.lang.Object ref = vehId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vehId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 车辆id
     * </pre>
     *
     * <code>string vehId = 1;</code>
     * @return The bytes for vehId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getVehIdBytes() {
      java.lang.Object ref = vehId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vehId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATENO_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object plateNo_ = "";
    /**
     * <pre>
     * 车牌
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     * @return The plateNo.
     */
    @java.lang.Override
    public java.lang.String getPlateNo() {
      java.lang.Object ref = plateNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        plateNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 车牌
     * </pre>
     *
     * <code>string plateNo = 2;</code>
     * @return The bytes for plateNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlateNoBytes() {
      java.lang.Object ref = plateNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        plateNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LONGITUDE_FIELD_NUMBER = 3;
    private double longitude_ = 0D;
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>double longitude = 3;</code>
     * @return The longitude.
     */
    @java.lang.Override
    public double getLongitude() {
      return longitude_;
    }

    public static final int LATITUDE_FIELD_NUMBER = 4;
    private double latitude_ = 0D;
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>double latitude = 4;</code>
     * @return The latitude.
     */
    @java.lang.Override
    public double getLatitude() {
      return latitude_;
    }

    public static final int ELEVATION_FIELD_NUMBER = 5;
    private double elevation_ = 0D;
    /**
     * <pre>
     * 高程
     * </pre>
     *
     * <code>double elevation = 5;</code>
     * @return The elevation.
     */
    @java.lang.Override
    public double getElevation() {
      return elevation_;
    }

    public static final int TIMESTAMPGNSS_FIELD_NUMBER = 6;
    private long timestampGNSS_ = 0L;
    /**
     * <pre>
     *GNSS 时间戳
     * </pre>
     *
     * <code>int64 timestampGNSS = 6;</code>
     * @return The timestampGNSS.
     */
    @java.lang.Override
    public long getTimestampGNSS() {
      return timestampGNSS_;
    }

    public static final int VELOCITYGNSS_FIELD_NUMBER = 7;
    private double velocityGNSS_ = 0D;
    /**
     * <pre>
     *GNSS 速度
     * </pre>
     *
     * <code>double velocityGNSS = 7;</code>
     * @return The velocityGNSS.
     */
    @java.lang.Override
    public double getVelocityGNSS() {
      return velocityGNSS_;
    }

    public static final int HEADING_FIELD_NUMBER = 8;
    private long heading_ = 0L;
    /**
     * <pre>
     *航向角
     * </pre>
     *
     * <code>int64 heading = 8;</code>
     * @return The heading.
     */
    @java.lang.Override
    public long getHeading() {
      return heading_;
    }

    public static final int TAPPOS_FIELD_NUMBER = 9;
    private int tapPos_ = 0;
    /**
     * <pre>
     *  档位
     * </pre>
     *
     * <code>int32 tapPos = 9;</code>
     * @return The tapPos.
     */
    @java.lang.Override
    public int getTapPos() {
      return tapPos_;
    }

    public static final int STEERINGANGLE_FIELD_NUMBER = 10;
    private int steeringAngle_ = 0;
    /**
     * <pre>
     *  方向盘转角
     * </pre>
     *
     * <code>int32 steeringAngle = 10;</code>
     * @return The steeringAngle.
     */
    @java.lang.Override
    public int getSteeringAngle() {
      return steeringAngle_;
    }

    public static final int SPEED_FIELD_NUMBER = 11;
    private double speed_ = 0D;
    /**
     * <pre>
     *  当前车速
     * </pre>
     *
     * <code>double speed = 11;</code>
     * @return The speed.
     */
    @java.lang.Override
    public double getSpeed() {
      return speed_;
    }

    public static final int ACCELERATIONLON_FIELD_NUMBER = 12;
    private double accelerationLon_ = 0D;
    /**
     * <pre>
     *  纵向加速度
     * </pre>
     *
     * <code>double accelerationLon = 12;</code>
     * @return The accelerationLon.
     */
    @java.lang.Override
    public double getAccelerationLon() {
      return accelerationLon_;
    }

    public static final int ACCELERATIONLAT_FIELD_NUMBER = 13;
    private double accelerationLat_ = 0D;
    /**
     * <pre>
     *  横向加速度
     * </pre>
     *
     * <code>double accelerationLat = 13;</code>
     * @return The accelerationLat.
     */
    @java.lang.Override
    public double getAccelerationLat() {
      return accelerationLat_;
    }

    public static final int ACCELERATIONVER_FIELD_NUMBER = 14;
    private double accelerationVer_ = 0D;
    /**
     * <pre>
     *  垂向加速度
     * </pre>
     *
     * <code>double accelerationVer = 14;</code>
     * @return The accelerationVer.
     */
    @java.lang.Override
    public double getAccelerationVer() {
      return accelerationVer_;
    }

    public static final int YAWRATE_FIELD_NUMBER = 15;
    private double yawRate_ = 0D;
    /**
     * <pre>
     *  横摆角速度
     * </pre>
     *
     * <code>double yawRate = 15;</code>
     * @return The yawRate.
     */
    @java.lang.Override
    public double getYawRate() {
      return yawRate_;
    }

    public static final int ACCELPOS_FIELD_NUMBER = 16;
    private double accelPos_ = 0D;
    /**
     * <pre>
     * 油门开度
     * </pre>
     *
     * <code>double accelPos = 16;</code>
     * @return The accelPos.
     */
    @java.lang.Override
    public double getAccelPos() {
      return accelPos_;
    }

    public static final int BRAKEFLAG_FIELD_NUMBER = 17;
    private int brakeFlag_ = 0;
    /**
     * <pre>
     *   制动踏板开关
     * </pre>
     *
     * <code>int32 brakeFlag = 17;</code>
     * @return The brakeFlag.
     */
    @java.lang.Override
    public int getBrakeFlag() {
      return brakeFlag_;
    }

    public static final int BRAKEPOS_FIELD_NUMBER = 18;
    private double brakePos_ = 0D;
    /**
     * <pre>
     *  制动踏板开度
     * </pre>
     *
     * <code>double brakePos = 18;</code>
     * @return The brakePos.
     */
    @java.lang.Override
    public double getBrakePos() {
      return brakePos_;
    }

    public static final int DRIVEMODE_FIELD_NUMBER = 19;
    private int driveMode_ = 0;
    /**
     * <pre>
     * 车辆驾驶模式
     * </pre>
     *
     * <code>int32 driveMode = 19;</code>
     * @return The driveMode.
     */
    @java.lang.Override
    public int getDriveMode() {
      return driveMode_;
    }

    public static final int TIREPRESSURE_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList tirePressure_;
    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @return A list containing the tirePressure.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getTirePressureList() {
      return tirePressure_;
    }
    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @return The count of tirePressure.
     */
    public int getTirePressureCount() {
      return tirePressure_.size();
    }
    /**
     * <pre>
     * 胎压
     * </pre>
     *
     * <code>repeated int32 tirePressure = 20;</code>
     * @param index The index of the element to return.
     * @return The tirePressure at the given index.
     */
    public int getTirePressure(int index) {
      return tirePressure_.getInt(index);
    }
    private int tirePressureMemoizedSerializedSize = -1;

    public static final int AIRBAG_FIELD_NUMBER = 21;
    private int airBag_ = 0;
    /**
     * <pre>
     * 安全气囊状态
     * </pre>
     *
     * <code>int32 airBag = 21;</code>
     * @return The airBag.
     */
    @java.lang.Override
    public int getAirBag() {
      return airBag_;
    }

    public static final int FUELGAUGE_FIELD_NUMBER = 22;
    private int fuelGauge_ = 0;
    /**
     * <pre>
     * 剩余油量
     * </pre>
     *
     * <code>int32 fuelGauge = 22;</code>
     * @return The fuelGauge.
     */
    @java.lang.Override
    public int getFuelGauge() {
      return fuelGauge_;
    }

    public static final int SOC_FIELD_NUMBER = 23;
    private int soc_ = 0;
    /**
     * <pre>
     * 剩余电池电量
     * </pre>
     *
     * <code>int32 soc = 23;</code>
     * @return The soc.
     */
    @java.lang.Override
    public int getSoc() {
      return soc_;
    }

    public static final int EPB_FIELD_NUMBER = 24;
    private int epb_ = 0;
    /**
     * <pre>
     * 电子手刹状态
     * </pre>
     *
     * <code>int32 epb = 24;</code>
     * @return The epb.
     */
    @java.lang.Override
    public int getEpb() {
      return epb_;
    }

    public static final int WHEELSPEEDFL_FIELD_NUMBER = 25;
    private double wheelSpeedFL_ = 0D;
    /**
     * <pre>
     * 前左轮速
     * </pre>
     *
     * <code>double wheelSpeedFL = 25;</code>
     * @return The wheelSpeedFL.
     */
    @java.lang.Override
    public double getWheelSpeedFL() {
      return wheelSpeedFL_;
    }

    public static final int WHEELSPEEDFR_FIELD_NUMBER = 26;
    private double wheelSpeedFR_ = 0D;
    /**
     * <pre>
     * 前右轮速
     * </pre>
     *
     * <code>double wheelSpeedFR = 26;</code>
     * @return The wheelSpeedFR.
     */
    @java.lang.Override
    public double getWheelSpeedFR() {
      return wheelSpeedFR_;
    }

    public static final int WHEELSPEEDRL_FIELD_NUMBER = 27;
    private double wheelSpeedRL_ = 0D;
    /**
     * <pre>
     * 后左轮速
     * </pre>
     *
     * <code>double wheelSpeedRL = 27;</code>
     * @return The wheelSpeedRL.
     */
    @java.lang.Override
    public double getWheelSpeedRL() {
      return wheelSpeedRL_;
    }

    public static final int WHEELSPEEDRR_FIELD_NUMBER = 28;
    private double wheelSpeedRR_ = 0D;
    /**
     * <pre>
     * 后右轮速
     * </pre>
     *
     * <code>double wheelSpeedRR = 28;</code>
     * @return The wheelSpeedRR.
     */
    @java.lang.Override
    public double getWheelSpeedRR() {
      return wheelSpeedRR_;
    }

    public static final int MOTORSPEED_FIELD_NUMBER = 29;
    private double motorSpeed_ = 0D;
    /**
     * <pre>
     * 电机转速
     * </pre>
     *
     * <code>double motorSpeed = 29;</code>
     * @return The motorSpeed.
     */
    @java.lang.Override
    public double getMotorSpeed() {
      return motorSpeed_;
    }

    public static final int MOTORTORQUE_FIELD_NUMBER = 30;
    private double motorTorque_ = 0D;
    /**
     * <pre>
     * 电机扭矩
     * </pre>
     *
     * <code>double motorTorque = 30;</code>
     * @return The motorTorque.
     */
    @java.lang.Override
    public double getMotorTorque() {
      return motorTorque_;
    }

    public static final int EXT_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString ext_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * G524 二进制数据，数据结构参考 planning
     * </pre>
     *
     * <code>bytes ext = 31;</code>
     * @return The ext.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExt() {
      return ext_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vehId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, vehId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(plateNo_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, plateNo_);
      }
      if (java.lang.Double.doubleToRawLongBits(longitude_) != 0) {
        output.writeDouble(3, longitude_);
      }
      if (java.lang.Double.doubleToRawLongBits(latitude_) != 0) {
        output.writeDouble(4, latitude_);
      }
      if (java.lang.Double.doubleToRawLongBits(elevation_) != 0) {
        output.writeDouble(5, elevation_);
      }
      if (timestampGNSS_ != 0L) {
        output.writeInt64(6, timestampGNSS_);
      }
      if (java.lang.Double.doubleToRawLongBits(velocityGNSS_) != 0) {
        output.writeDouble(7, velocityGNSS_);
      }
      if (heading_ != 0L) {
        output.writeInt64(8, heading_);
      }
      if (tapPos_ != 0) {
        output.writeInt32(9, tapPos_);
      }
      if (steeringAngle_ != 0) {
        output.writeInt32(10, steeringAngle_);
      }
      if (java.lang.Double.doubleToRawLongBits(speed_) != 0) {
        output.writeDouble(11, speed_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationLon_) != 0) {
        output.writeDouble(12, accelerationLon_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationLat_) != 0) {
        output.writeDouble(13, accelerationLat_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationVer_) != 0) {
        output.writeDouble(14, accelerationVer_);
      }
      if (java.lang.Double.doubleToRawLongBits(yawRate_) != 0) {
        output.writeDouble(15, yawRate_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelPos_) != 0) {
        output.writeDouble(16, accelPos_);
      }
      if (brakeFlag_ != 0) {
        output.writeInt32(17, brakeFlag_);
      }
      if (java.lang.Double.doubleToRawLongBits(brakePos_) != 0) {
        output.writeDouble(18, brakePos_);
      }
      if (driveMode_ != 0) {
        output.writeInt32(19, driveMode_);
      }
      if (getTirePressureList().size() > 0) {
        output.writeUInt32NoTag(162);
        output.writeUInt32NoTag(tirePressureMemoizedSerializedSize);
      }
      for (int i = 0; i < tirePressure_.size(); i++) {
        output.writeInt32NoTag(tirePressure_.getInt(i));
      }
      if (airBag_ != 0) {
        output.writeInt32(21, airBag_);
      }
      if (fuelGauge_ != 0) {
        output.writeInt32(22, fuelGauge_);
      }
      if (soc_ != 0) {
        output.writeInt32(23, soc_);
      }
      if (epb_ != 0) {
        output.writeInt32(24, epb_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedFL_) != 0) {
        output.writeDouble(25, wheelSpeedFL_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedFR_) != 0) {
        output.writeDouble(26, wheelSpeedFR_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedRL_) != 0) {
        output.writeDouble(27, wheelSpeedRL_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedRR_) != 0) {
        output.writeDouble(28, wheelSpeedRR_);
      }
      if (java.lang.Double.doubleToRawLongBits(motorSpeed_) != 0) {
        output.writeDouble(29, motorSpeed_);
      }
      if (java.lang.Double.doubleToRawLongBits(motorTorque_) != 0) {
        output.writeDouble(30, motorTorque_);
      }
      if (!ext_.isEmpty()) {
        output.writeBytes(31, ext_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vehId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, vehId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(plateNo_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, plateNo_);
      }
      if (java.lang.Double.doubleToRawLongBits(longitude_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(3, longitude_);
      }
      if (java.lang.Double.doubleToRawLongBits(latitude_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(4, latitude_);
      }
      if (java.lang.Double.doubleToRawLongBits(elevation_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, elevation_);
      }
      if (timestampGNSS_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, timestampGNSS_);
      }
      if (java.lang.Double.doubleToRawLongBits(velocityGNSS_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(7, velocityGNSS_);
      }
      if (heading_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, heading_);
      }
      if (tapPos_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, tapPos_);
      }
      if (steeringAngle_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, steeringAngle_);
      }
      if (java.lang.Double.doubleToRawLongBits(speed_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(11, speed_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationLon_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(12, accelerationLon_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationLat_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(13, accelerationLat_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelerationVer_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(14, accelerationVer_);
      }
      if (java.lang.Double.doubleToRawLongBits(yawRate_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(15, yawRate_);
      }
      if (java.lang.Double.doubleToRawLongBits(accelPos_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(16, accelPos_);
      }
      if (brakeFlag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(17, brakeFlag_);
      }
      if (java.lang.Double.doubleToRawLongBits(brakePos_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(18, brakePos_);
      }
      if (driveMode_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(19, driveMode_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < tirePressure_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(tirePressure_.getInt(i));
        }
        size += dataSize;
        if (!getTirePressureList().isEmpty()) {
          size += 2;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        tirePressureMemoizedSerializedSize = dataSize;
      }
      if (airBag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(21, airBag_);
      }
      if (fuelGauge_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(22, fuelGauge_);
      }
      if (soc_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(23, soc_);
      }
      if (epb_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(24, epb_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedFL_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(25, wheelSpeedFL_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedFR_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(26, wheelSpeedFR_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedRL_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(27, wheelSpeedRL_);
      }
      if (java.lang.Double.doubleToRawLongBits(wheelSpeedRR_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(28, wheelSpeedRR_);
      }
      if (java.lang.Double.doubleToRawLongBits(motorSpeed_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(29, motorSpeed_);
      }
      if (java.lang.Double.doubleToRawLongBits(motorTorque_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(30, motorTorque_);
      }
      if (!ext_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, ext_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData)) {
        return super.equals(obj);
      }
      com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData other = (com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData) obj;

      if (!getVehId()
          .equals(other.getVehId())) return false;
      if (!getPlateNo()
          .equals(other.getPlateNo())) return false;
      if (java.lang.Double.doubleToLongBits(getLongitude())
          != java.lang.Double.doubleToLongBits(
              other.getLongitude())) return false;
      if (java.lang.Double.doubleToLongBits(getLatitude())
          != java.lang.Double.doubleToLongBits(
              other.getLatitude())) return false;
      if (java.lang.Double.doubleToLongBits(getElevation())
          != java.lang.Double.doubleToLongBits(
              other.getElevation())) return false;
      if (getTimestampGNSS()
          != other.getTimestampGNSS()) return false;
      if (java.lang.Double.doubleToLongBits(getVelocityGNSS())
          != java.lang.Double.doubleToLongBits(
              other.getVelocityGNSS())) return false;
      if (getHeading()
          != other.getHeading()) return false;
      if (getTapPos()
          != other.getTapPos()) return false;
      if (getSteeringAngle()
          != other.getSteeringAngle()) return false;
      if (java.lang.Double.doubleToLongBits(getSpeed())
          != java.lang.Double.doubleToLongBits(
              other.getSpeed())) return false;
      if (java.lang.Double.doubleToLongBits(getAccelerationLon())
          != java.lang.Double.doubleToLongBits(
              other.getAccelerationLon())) return false;
      if (java.lang.Double.doubleToLongBits(getAccelerationLat())
          != java.lang.Double.doubleToLongBits(
              other.getAccelerationLat())) return false;
      if (java.lang.Double.doubleToLongBits(getAccelerationVer())
          != java.lang.Double.doubleToLongBits(
              other.getAccelerationVer())) return false;
      if (java.lang.Double.doubleToLongBits(getYawRate())
          != java.lang.Double.doubleToLongBits(
              other.getYawRate())) return false;
      if (java.lang.Double.doubleToLongBits(getAccelPos())
          != java.lang.Double.doubleToLongBits(
              other.getAccelPos())) return false;
      if (getBrakeFlag()
          != other.getBrakeFlag()) return false;
      if (java.lang.Double.doubleToLongBits(getBrakePos())
          != java.lang.Double.doubleToLongBits(
              other.getBrakePos())) return false;
      if (getDriveMode()
          != other.getDriveMode()) return false;
      if (!getTirePressureList()
          .equals(other.getTirePressureList())) return false;
      if (getAirBag()
          != other.getAirBag()) return false;
      if (getFuelGauge()
          != other.getFuelGauge()) return false;
      if (getSoc()
          != other.getSoc()) return false;
      if (getEpb()
          != other.getEpb()) return false;
      if (java.lang.Double.doubleToLongBits(getWheelSpeedFL())
          != java.lang.Double.doubleToLongBits(
              other.getWheelSpeedFL())) return false;
      if (java.lang.Double.doubleToLongBits(getWheelSpeedFR())
          != java.lang.Double.doubleToLongBits(
              other.getWheelSpeedFR())) return false;
      if (java.lang.Double.doubleToLongBits(getWheelSpeedRL())
          != java.lang.Double.doubleToLongBits(
              other.getWheelSpeedRL())) return false;
      if (java.lang.Double.doubleToLongBits(getWheelSpeedRR())
          != java.lang.Double.doubleToLongBits(
              other.getWheelSpeedRR())) return false;
      if (java.lang.Double.doubleToLongBits(getMotorSpeed())
          != java.lang.Double.doubleToLongBits(
              other.getMotorSpeed())) return false;
      if (java.lang.Double.doubleToLongBits(getMotorTorque())
          != java.lang.Double.doubleToLongBits(
              other.getMotorTorque())) return false;
      if (!getExt()
          .equals(other.getExt())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VEHID_FIELD_NUMBER;
      hash = (53 * hash) + getVehId().hashCode();
      hash = (37 * hash) + PLATENO_FIELD_NUMBER;
      hash = (53 * hash) + getPlateNo().hashCode();
      hash = (37 * hash) + LONGITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getLongitude()));
      hash = (37 * hash) + LATITUDE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getLatitude()));
      hash = (37 * hash) + ELEVATION_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getElevation()));
      hash = (37 * hash) + TIMESTAMPGNSS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestampGNSS());
      hash = (37 * hash) + VELOCITYGNSS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getVelocityGNSS()));
      hash = (37 * hash) + HEADING_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHeading());
      hash = (37 * hash) + TAPPOS_FIELD_NUMBER;
      hash = (53 * hash) + getTapPos();
      hash = (37 * hash) + STEERINGANGLE_FIELD_NUMBER;
      hash = (53 * hash) + getSteeringAngle();
      hash = (37 * hash) + SPEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getSpeed()));
      hash = (37 * hash) + ACCELERATIONLON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAccelerationLon()));
      hash = (37 * hash) + ACCELERATIONLAT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAccelerationLat()));
      hash = (37 * hash) + ACCELERATIONVER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAccelerationVer()));
      hash = (37 * hash) + YAWRATE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getYawRate()));
      hash = (37 * hash) + ACCELPOS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAccelPos()));
      hash = (37 * hash) + BRAKEFLAG_FIELD_NUMBER;
      hash = (53 * hash) + getBrakeFlag();
      hash = (37 * hash) + BRAKEPOS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getBrakePos()));
      hash = (37 * hash) + DRIVEMODE_FIELD_NUMBER;
      hash = (53 * hash) + getDriveMode();
      if (getTirePressureCount() > 0) {
        hash = (37 * hash) + TIREPRESSURE_FIELD_NUMBER;
        hash = (53 * hash) + getTirePressureList().hashCode();
      }
      hash = (37 * hash) + AIRBAG_FIELD_NUMBER;
      hash = (53 * hash) + getAirBag();
      hash = (37 * hash) + FUELGAUGE_FIELD_NUMBER;
      hash = (53 * hash) + getFuelGauge();
      hash = (37 * hash) + SOC_FIELD_NUMBER;
      hash = (53 * hash) + getSoc();
      hash = (37 * hash) + EPB_FIELD_NUMBER;
      hash = (53 * hash) + getEpb();
      hash = (37 * hash) + WHEELSPEEDFL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getWheelSpeedFL()));
      hash = (37 * hash) + WHEELSPEEDFR_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getWheelSpeedFR()));
      hash = (37 * hash) + WHEELSPEEDRL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getWheelSpeedRL()));
      hash = (37 * hash) + WHEELSPEEDRR_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getWheelSpeedRR()));
      hash = (37 * hash) + MOTORSPEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getMotorSpeed()));
      hash = (37 * hash) + MOTORTORQUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getMotorTorque()));
      hash = (37 * hash) + EXT_FIELD_NUMBER;
      hash = (53 * hash) + getExt().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code proto.VehData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:proto.VehData)
        com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.les.its.scm.busdata.himalaya.proto.BsmDTO.internal_static_proto_VehData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.les.its.scm.busdata.himalaya.proto.BsmDTO.internal_static_proto_VehData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.class, com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.Builder.class);
      }

      // Construct using com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vehId_ = "";
        plateNo_ = "";
        longitude_ = 0D;
        latitude_ = 0D;
        elevation_ = 0D;
        timestampGNSS_ = 0L;
        velocityGNSS_ = 0D;
        heading_ = 0L;
        tapPos_ = 0;
        steeringAngle_ = 0;
        speed_ = 0D;
        accelerationLon_ = 0D;
        accelerationLat_ = 0D;
        accelerationVer_ = 0D;
        yawRate_ = 0D;
        accelPos_ = 0D;
        brakeFlag_ = 0;
        brakePos_ = 0D;
        driveMode_ = 0;
        tirePressure_ = emptyIntList();
        airBag_ = 0;
        fuelGauge_ = 0;
        soc_ = 0;
        epb_ = 0;
        wheelSpeedFL_ = 0D;
        wheelSpeedFR_ = 0D;
        wheelSpeedRL_ = 0D;
        wheelSpeedRR_ = 0D;
        motorSpeed_ = 0D;
        motorTorque_ = 0D;
        ext_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.les.its.scm.busdata.himalaya.proto.BsmDTO.internal_static_proto_VehData_descriptor;
      }

      @java.lang.Override
      public com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData getDefaultInstanceForType() {
        return com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.getDefaultInstance();
      }

      @java.lang.Override
      public com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData build() {
        com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData buildPartial() {
        com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData result = new com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData result) {
        if (((bitField0_ & 0x00080000) != 0)) {
          tirePressure_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00080000);
        }
        result.tirePressure_ = tirePressure_;
      }

      private void buildPartial0(com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vehId_ = vehId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.plateNo_ = plateNo_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.longitude_ = longitude_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.latitude_ = latitude_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.elevation_ = elevation_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.timestampGNSS_ = timestampGNSS_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.velocityGNSS_ = velocityGNSS_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.heading_ = heading_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.tapPos_ = tapPos_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.steeringAngle_ = steeringAngle_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.speed_ = speed_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.accelerationLon_ = accelerationLon_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.accelerationLat_ = accelerationLat_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.accelerationVer_ = accelerationVer_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.yawRate_ = yawRate_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.accelPos_ = accelPos_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.brakeFlag_ = brakeFlag_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.brakePos_ = brakePos_;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.driveMode_ = driveMode_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.airBag_ = airBag_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.fuelGauge_ = fuelGauge_;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.soc_ = soc_;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.epb_ = epb_;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.wheelSpeedFL_ = wheelSpeedFL_;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.wheelSpeedFR_ = wheelSpeedFR_;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.wheelSpeedRL_ = wheelSpeedRL_;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.wheelSpeedRR_ = wheelSpeedRR_;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.motorSpeed_ = motorSpeed_;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.motorTorque_ = motorTorque_;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.ext_ = ext_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData) {
          return mergeFrom((com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData other) {
        if (other == com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData.getDefaultInstance()) return this;
        if (!other.getVehId().isEmpty()) {
          vehId_ = other.vehId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getPlateNo().isEmpty()) {
          plateNo_ = other.plateNo_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.getLongitude() != 0D) {
          setLongitude(other.getLongitude());
        }
        if (other.getLatitude() != 0D) {
          setLatitude(other.getLatitude());
        }
        if (other.getElevation() != 0D) {
          setElevation(other.getElevation());
        }
        if (other.getTimestampGNSS() != 0L) {
          setTimestampGNSS(other.getTimestampGNSS());
        }
        if (other.getVelocityGNSS() != 0D) {
          setVelocityGNSS(other.getVelocityGNSS());
        }
        if (other.getHeading() != 0L) {
          setHeading(other.getHeading());
        }
        if (other.getTapPos() != 0) {
          setTapPos(other.getTapPos());
        }
        if (other.getSteeringAngle() != 0) {
          setSteeringAngle(other.getSteeringAngle());
        }
        if (other.getSpeed() != 0D) {
          setSpeed(other.getSpeed());
        }
        if (other.getAccelerationLon() != 0D) {
          setAccelerationLon(other.getAccelerationLon());
        }
        if (other.getAccelerationLat() != 0D) {
          setAccelerationLat(other.getAccelerationLat());
        }
        if (other.getAccelerationVer() != 0D) {
          setAccelerationVer(other.getAccelerationVer());
        }
        if (other.getYawRate() != 0D) {
          setYawRate(other.getYawRate());
        }
        if (other.getAccelPos() != 0D) {
          setAccelPos(other.getAccelPos());
        }
        if (other.getBrakeFlag() != 0) {
          setBrakeFlag(other.getBrakeFlag());
        }
        if (other.getBrakePos() != 0D) {
          setBrakePos(other.getBrakePos());
        }
        if (other.getDriveMode() != 0) {
          setDriveMode(other.getDriveMode());
        }
        if (!other.tirePressure_.isEmpty()) {
          if (tirePressure_.isEmpty()) {
            tirePressure_ = other.tirePressure_;
            bitField0_ = (bitField0_ & ~0x00080000);
          } else {
            ensureTirePressureIsMutable();
            tirePressure_.addAll(other.tirePressure_);
          }
          onChanged();
        }
        if (other.getAirBag() != 0) {
          setAirBag(other.getAirBag());
        }
        if (other.getFuelGauge() != 0) {
          setFuelGauge(other.getFuelGauge());
        }
        if (other.getSoc() != 0) {
          setSoc(other.getSoc());
        }
        if (other.getEpb() != 0) {
          setEpb(other.getEpb());
        }
        if (other.getWheelSpeedFL() != 0D) {
          setWheelSpeedFL(other.getWheelSpeedFL());
        }
        if (other.getWheelSpeedFR() != 0D) {
          setWheelSpeedFR(other.getWheelSpeedFR());
        }
        if (other.getWheelSpeedRL() != 0D) {
          setWheelSpeedRL(other.getWheelSpeedRL());
        }
        if (other.getWheelSpeedRR() != 0D) {
          setWheelSpeedRR(other.getWheelSpeedRR());
        }
        if (other.getMotorSpeed() != 0D) {
          setMotorSpeed(other.getMotorSpeed());
        }
        if (other.getMotorTorque() != 0D) {
          setMotorTorque(other.getMotorTorque());
        }
        if (other.getExt() != com.google.protobuf.ByteString.EMPTY) {
          setExt(other.getExt());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                vehId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                plateNo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 25: {
                longitude_ = input.readDouble();
                bitField0_ |= 0x00000004;
                break;
              } // case 25
              case 33: {
                latitude_ = input.readDouble();
                bitField0_ |= 0x00000008;
                break;
              } // case 33
              case 41: {
                elevation_ = input.readDouble();
                bitField0_ |= 0x00000010;
                break;
              } // case 41
              case 48: {
                timestampGNSS_ = input.readInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 57: {
                velocityGNSS_ = input.readDouble();
                bitField0_ |= 0x00000040;
                break;
              } // case 57
              case 64: {
                heading_ = input.readInt64();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                tapPos_ = input.readInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                steeringAngle_ = input.readInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 89: {
                speed_ = input.readDouble();
                bitField0_ |= 0x00000400;
                break;
              } // case 89
              case 97: {
                accelerationLon_ = input.readDouble();
                bitField0_ |= 0x00000800;
                break;
              } // case 97
              case 105: {
                accelerationLat_ = input.readDouble();
                bitField0_ |= 0x00001000;
                break;
              } // case 105
              case 113: {
                accelerationVer_ = input.readDouble();
                bitField0_ |= 0x00002000;
                break;
              } // case 113
              case 121: {
                yawRate_ = input.readDouble();
                bitField0_ |= 0x00004000;
                break;
              } // case 121
              case 129: {
                accelPos_ = input.readDouble();
                bitField0_ |= 0x00008000;
                break;
              } // case 129
              case 136: {
                brakeFlag_ = input.readInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 145: {
                brakePos_ = input.readDouble();
                bitField0_ |= 0x00020000;
                break;
              } // case 145
              case 152: {
                driveMode_ = input.readInt32();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                int v = input.readInt32();
                ensureTirePressureIsMutable();
                tirePressure_.addInt(v);
                break;
              } // case 160
              case 162: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureTirePressureIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  tirePressure_.addInt(input.readInt32());
                }
                input.popLimit(limit);
                break;
              } // case 162
              case 168: {
                airBag_ = input.readInt32();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 176: {
                fuelGauge_ = input.readInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 184: {
                soc_ = input.readInt32();
                bitField0_ |= 0x00400000;
                break;
              } // case 184
              case 192: {
                epb_ = input.readInt32();
                bitField0_ |= 0x00800000;
                break;
              } // case 192
              case 201: {
                wheelSpeedFL_ = input.readDouble();
                bitField0_ |= 0x01000000;
                break;
              } // case 201
              case 209: {
                wheelSpeedFR_ = input.readDouble();
                bitField0_ |= 0x02000000;
                break;
              } // case 209
              case 217: {
                wheelSpeedRL_ = input.readDouble();
                bitField0_ |= 0x04000000;
                break;
              } // case 217
              case 225: {
                wheelSpeedRR_ = input.readDouble();
                bitField0_ |= 0x08000000;
                break;
              } // case 225
              case 233: {
                motorSpeed_ = input.readDouble();
                bitField0_ |= 0x10000000;
                break;
              } // case 233
              case 241: {
                motorTorque_ = input.readDouble();
                bitField0_ |= 0x20000000;
                break;
              } // case 241
              case 250: {
                ext_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object vehId_ = "";
      /**
       * <pre>
       * 车辆id
       * </pre>
       *
       * <code>string vehId = 1;</code>
       * @return The vehId.
       */
      public java.lang.String getVehId() {
        java.lang.Object ref = vehId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          vehId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 车辆id
       * </pre>
       *
       * <code>string vehId = 1;</code>
       * @return The bytes for vehId.
       */
      public com.google.protobuf.ByteString
          getVehIdBytes() {
        java.lang.Object ref = vehId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          vehId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 车辆id
       * </pre>
       *
       * <code>string vehId = 1;</code>
       * @param value The vehId to set.
       * @return This builder for chaining.
       */
      public Builder setVehId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        vehId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 车辆id
       * </pre>
       *
       * <code>string vehId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVehId() {
        vehId_ = getDefaultInstance().getVehId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 车辆id
       * </pre>
       *
       * <code>string vehId = 1;</code>
       * @param value The bytes for vehId to set.
       * @return This builder for chaining.
       */
      public Builder setVehIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        vehId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object plateNo_ = "";
      /**
       * <pre>
       * 车牌
       * </pre>
       *
       * <code>string plateNo = 2;</code>
       * @return The plateNo.
       */
      public java.lang.String getPlateNo() {
        java.lang.Object ref = plateNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          plateNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 车牌
       * </pre>
       *
       * <code>string plateNo = 2;</code>
       * @return The bytes for plateNo.
       */
      public com.google.protobuf.ByteString
          getPlateNoBytes() {
        java.lang.Object ref = plateNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          plateNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 车牌
       * </pre>
       *
       * <code>string plateNo = 2;</code>
       * @param value The plateNo to set.
       * @return This builder for chaining.
       */
      public Builder setPlateNo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        plateNo_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 车牌
       * </pre>
       *
       * <code>string plateNo = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlateNo() {
        plateNo_ = getDefaultInstance().getPlateNo();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 车牌
       * </pre>
       *
       * <code>string plateNo = 2;</code>
       * @param value The bytes for plateNo to set.
       * @return This builder for chaining.
       */
      public Builder setPlateNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        plateNo_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private double longitude_ ;
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>double longitude = 3;</code>
       * @return The longitude.
       */
      @java.lang.Override
      public double getLongitude() {
        return longitude_;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>double longitude = 3;</code>
       * @param value The longitude to set.
       * @return This builder for chaining.
       */
      public Builder setLongitude(double value) {
        
        longitude_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>double longitude = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLongitude() {
        bitField0_ = (bitField0_ & ~0x00000004);
        longitude_ = 0D;
        onChanged();
        return this;
      }

      private double latitude_ ;
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>double latitude = 4;</code>
       * @return The latitude.
       */
      @java.lang.Override
      public double getLatitude() {
        return latitude_;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>double latitude = 4;</code>
       * @param value The latitude to set.
       * @return This builder for chaining.
       */
      public Builder setLatitude(double value) {
        
        latitude_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>double latitude = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLatitude() {
        bitField0_ = (bitField0_ & ~0x00000008);
        latitude_ = 0D;
        onChanged();
        return this;
      }

      private double elevation_ ;
      /**
       * <pre>
       * 高程
       * </pre>
       *
       * <code>double elevation = 5;</code>
       * @return The elevation.
       */
      @java.lang.Override
      public double getElevation() {
        return elevation_;
      }
      /**
       * <pre>
       * 高程
       * </pre>
       *
       * <code>double elevation = 5;</code>
       * @param value The elevation to set.
       * @return This builder for chaining.
       */
      public Builder setElevation(double value) {
        
        elevation_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 高程
       * </pre>
       *
       * <code>double elevation = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearElevation() {
        bitField0_ = (bitField0_ & ~0x00000010);
        elevation_ = 0D;
        onChanged();
        return this;
      }

      private long timestampGNSS_ ;
      /**
       * <pre>
       *GNSS 时间戳
       * </pre>
       *
       * <code>int64 timestampGNSS = 6;</code>
       * @return The timestampGNSS.
       */
      @java.lang.Override
      public long getTimestampGNSS() {
        return timestampGNSS_;
      }
      /**
       * <pre>
       *GNSS 时间戳
       * </pre>
       *
       * <code>int64 timestampGNSS = 6;</code>
       * @param value The timestampGNSS to set.
       * @return This builder for chaining.
       */
      public Builder setTimestampGNSS(long value) {
        
        timestampGNSS_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *GNSS 时间戳
       * </pre>
       *
       * <code>int64 timestampGNSS = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestampGNSS() {
        bitField0_ = (bitField0_ & ~0x00000020);
        timestampGNSS_ = 0L;
        onChanged();
        return this;
      }

      private double velocityGNSS_ ;
      /**
       * <pre>
       *GNSS 速度
       * </pre>
       *
       * <code>double velocityGNSS = 7;</code>
       * @return The velocityGNSS.
       */
      @java.lang.Override
      public double getVelocityGNSS() {
        return velocityGNSS_;
      }
      /**
       * <pre>
       *GNSS 速度
       * </pre>
       *
       * <code>double velocityGNSS = 7;</code>
       * @param value The velocityGNSS to set.
       * @return This builder for chaining.
       */
      public Builder setVelocityGNSS(double value) {
        
        velocityGNSS_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *GNSS 速度
       * </pre>
       *
       * <code>double velocityGNSS = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearVelocityGNSS() {
        bitField0_ = (bitField0_ & ~0x00000040);
        velocityGNSS_ = 0D;
        onChanged();
        return this;
      }

      private long heading_ ;
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>int64 heading = 8;</code>
       * @return The heading.
       */
      @java.lang.Override
      public long getHeading() {
        return heading_;
      }
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>int64 heading = 8;</code>
       * @param value The heading to set.
       * @return This builder for chaining.
       */
      public Builder setHeading(long value) {
        
        heading_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *航向角
       * </pre>
       *
       * <code>int64 heading = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeading() {
        bitField0_ = (bitField0_ & ~0x00000080);
        heading_ = 0L;
        onChanged();
        return this;
      }

      private int tapPos_ ;
      /**
       * <pre>
       *  档位
       * </pre>
       *
       * <code>int32 tapPos = 9;</code>
       * @return The tapPos.
       */
      @java.lang.Override
      public int getTapPos() {
        return tapPos_;
      }
      /**
       * <pre>
       *  档位
       * </pre>
       *
       * <code>int32 tapPos = 9;</code>
       * @param value The tapPos to set.
       * @return This builder for chaining.
       */
      public Builder setTapPos(int value) {
        
        tapPos_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  档位
       * </pre>
       *
       * <code>int32 tapPos = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearTapPos() {
        bitField0_ = (bitField0_ & ~0x00000100);
        tapPos_ = 0;
        onChanged();
        return this;
      }

      private int steeringAngle_ ;
      /**
       * <pre>
       *  方向盘转角
       * </pre>
       *
       * <code>int32 steeringAngle = 10;</code>
       * @return The steeringAngle.
       */
      @java.lang.Override
      public int getSteeringAngle() {
        return steeringAngle_;
      }
      /**
       * <pre>
       *  方向盘转角
       * </pre>
       *
       * <code>int32 steeringAngle = 10;</code>
       * @param value The steeringAngle to set.
       * @return This builder for chaining.
       */
      public Builder setSteeringAngle(int value) {
        
        steeringAngle_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  方向盘转角
       * </pre>
       *
       * <code>int32 steeringAngle = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSteeringAngle() {
        bitField0_ = (bitField0_ & ~0x00000200);
        steeringAngle_ = 0;
        onChanged();
        return this;
      }

      private double speed_ ;
      /**
       * <pre>
       *  当前车速
       * </pre>
       *
       * <code>double speed = 11;</code>
       * @return The speed.
       */
      @java.lang.Override
      public double getSpeed() {
        return speed_;
      }
      /**
       * <pre>
       *  当前车速
       * </pre>
       *
       * <code>double speed = 11;</code>
       * @param value The speed to set.
       * @return This builder for chaining.
       */
      public Builder setSpeed(double value) {
        
        speed_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  当前车速
       * </pre>
       *
       * <code>double speed = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000400);
        speed_ = 0D;
        onChanged();
        return this;
      }

      private double accelerationLon_ ;
      /**
       * <pre>
       *  纵向加速度
       * </pre>
       *
       * <code>double accelerationLon = 12;</code>
       * @return The accelerationLon.
       */
      @java.lang.Override
      public double getAccelerationLon() {
        return accelerationLon_;
      }
      /**
       * <pre>
       *  纵向加速度
       * </pre>
       *
       * <code>double accelerationLon = 12;</code>
       * @param value The accelerationLon to set.
       * @return This builder for chaining.
       */
      public Builder setAccelerationLon(double value) {
        
        accelerationLon_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  纵向加速度
       * </pre>
       *
       * <code>double accelerationLon = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccelerationLon() {
        bitField0_ = (bitField0_ & ~0x00000800);
        accelerationLon_ = 0D;
        onChanged();
        return this;
      }

      private double accelerationLat_ ;
      /**
       * <pre>
       *  横向加速度
       * </pre>
       *
       * <code>double accelerationLat = 13;</code>
       * @return The accelerationLat.
       */
      @java.lang.Override
      public double getAccelerationLat() {
        return accelerationLat_;
      }
      /**
       * <pre>
       *  横向加速度
       * </pre>
       *
       * <code>double accelerationLat = 13;</code>
       * @param value The accelerationLat to set.
       * @return This builder for chaining.
       */
      public Builder setAccelerationLat(double value) {
        
        accelerationLat_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  横向加速度
       * </pre>
       *
       * <code>double accelerationLat = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccelerationLat() {
        bitField0_ = (bitField0_ & ~0x00001000);
        accelerationLat_ = 0D;
        onChanged();
        return this;
      }

      private double accelerationVer_ ;
      /**
       * <pre>
       *  垂向加速度
       * </pre>
       *
       * <code>double accelerationVer = 14;</code>
       * @return The accelerationVer.
       */
      @java.lang.Override
      public double getAccelerationVer() {
        return accelerationVer_;
      }
      /**
       * <pre>
       *  垂向加速度
       * </pre>
       *
       * <code>double accelerationVer = 14;</code>
       * @param value The accelerationVer to set.
       * @return This builder for chaining.
       */
      public Builder setAccelerationVer(double value) {
        
        accelerationVer_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  垂向加速度
       * </pre>
       *
       * <code>double accelerationVer = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccelerationVer() {
        bitField0_ = (bitField0_ & ~0x00002000);
        accelerationVer_ = 0D;
        onChanged();
        return this;
      }

      private double yawRate_ ;
      /**
       * <pre>
       *  横摆角速度
       * </pre>
       *
       * <code>double yawRate = 15;</code>
       * @return The yawRate.
       */
      @java.lang.Override
      public double getYawRate() {
        return yawRate_;
      }
      /**
       * <pre>
       *  横摆角速度
       * </pre>
       *
       * <code>double yawRate = 15;</code>
       * @param value The yawRate to set.
       * @return This builder for chaining.
       */
      public Builder setYawRate(double value) {
        
        yawRate_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  横摆角速度
       * </pre>
       *
       * <code>double yawRate = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearYawRate() {
        bitField0_ = (bitField0_ & ~0x00004000);
        yawRate_ = 0D;
        onChanged();
        return this;
      }

      private double accelPos_ ;
      /**
       * <pre>
       * 油门开度
       * </pre>
       *
       * <code>double accelPos = 16;</code>
       * @return The accelPos.
       */
      @java.lang.Override
      public double getAccelPos() {
        return accelPos_;
      }
      /**
       * <pre>
       * 油门开度
       * </pre>
       *
       * <code>double accelPos = 16;</code>
       * @param value The accelPos to set.
       * @return This builder for chaining.
       */
      public Builder setAccelPos(double value) {
        
        accelPos_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 油门开度
       * </pre>
       *
       * <code>double accelPos = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccelPos() {
        bitField0_ = (bitField0_ & ~0x00008000);
        accelPos_ = 0D;
        onChanged();
        return this;
      }

      private int brakeFlag_ ;
      /**
       * <pre>
       *   制动踏板开关
       * </pre>
       *
       * <code>int32 brakeFlag = 17;</code>
       * @return The brakeFlag.
       */
      @java.lang.Override
      public int getBrakeFlag() {
        return brakeFlag_;
      }
      /**
       * <pre>
       *   制动踏板开关
       * </pre>
       *
       * <code>int32 brakeFlag = 17;</code>
       * @param value The brakeFlag to set.
       * @return This builder for chaining.
       */
      public Builder setBrakeFlag(int value) {
        
        brakeFlag_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *   制动踏板开关
       * </pre>
       *
       * <code>int32 brakeFlag = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearBrakeFlag() {
        bitField0_ = (bitField0_ & ~0x00010000);
        brakeFlag_ = 0;
        onChanged();
        return this;
      }

      private double brakePos_ ;
      /**
       * <pre>
       *  制动踏板开度
       * </pre>
       *
       * <code>double brakePos = 18;</code>
       * @return The brakePos.
       */
      @java.lang.Override
      public double getBrakePos() {
        return brakePos_;
      }
      /**
       * <pre>
       *  制动踏板开度
       * </pre>
       *
       * <code>double brakePos = 18;</code>
       * @param value The brakePos to set.
       * @return This builder for chaining.
       */
      public Builder setBrakePos(double value) {
        
        brakePos_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  制动踏板开度
       * </pre>
       *
       * <code>double brakePos = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearBrakePos() {
        bitField0_ = (bitField0_ & ~0x00020000);
        brakePos_ = 0D;
        onChanged();
        return this;
      }

      private int driveMode_ ;
      /**
       * <pre>
       * 车辆驾驶模式
       * </pre>
       *
       * <code>int32 driveMode = 19;</code>
       * @return The driveMode.
       */
      @java.lang.Override
      public int getDriveMode() {
        return driveMode_;
      }
      /**
       * <pre>
       * 车辆驾驶模式
       * </pre>
       *
       * <code>int32 driveMode = 19;</code>
       * @param value The driveMode to set.
       * @return This builder for chaining.
       */
      public Builder setDriveMode(int value) {
        
        driveMode_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 车辆驾驶模式
       * </pre>
       *
       * <code>int32 driveMode = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearDriveMode() {
        bitField0_ = (bitField0_ & ~0x00040000);
        driveMode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList tirePressure_ = emptyIntList();
      private void ensureTirePressureIsMutable() {
        if (!((bitField0_ & 0x00080000) != 0)) {
          tirePressure_ = mutableCopy(tirePressure_);
          bitField0_ |= 0x00080000;
        }
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @return A list containing the tirePressure.
       */
      public java.util.List<java.lang.Integer>
          getTirePressureList() {
        return ((bitField0_ & 0x00080000) != 0) ?
                 java.util.Collections.unmodifiableList(tirePressure_) : tirePressure_;
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @return The count of tirePressure.
       */
      public int getTirePressureCount() {
        return tirePressure_.size();
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @param index The index of the element to return.
       * @return The tirePressure at the given index.
       */
      public int getTirePressure(int index) {
        return tirePressure_.getInt(index);
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @param index The index to set the value at.
       * @param value The tirePressure to set.
       * @return This builder for chaining.
       */
      public Builder setTirePressure(
          int index, int value) {
        
        ensureTirePressureIsMutable();
        tirePressure_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @param value The tirePressure to add.
       * @return This builder for chaining.
       */
      public Builder addTirePressure(int value) {
        
        ensureTirePressureIsMutable();
        tirePressure_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @param values The tirePressure to add.
       * @return This builder for chaining.
       */
      public Builder addAllTirePressure(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureTirePressureIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, tirePressure_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 胎压
       * </pre>
       *
       * <code>repeated int32 tirePressure = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearTirePressure() {
        tirePressure_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }

      private int airBag_ ;
      /**
       * <pre>
       * 安全气囊状态
       * </pre>
       *
       * <code>int32 airBag = 21;</code>
       * @return The airBag.
       */
      @java.lang.Override
      public int getAirBag() {
        return airBag_;
      }
      /**
       * <pre>
       * 安全气囊状态
       * </pre>
       *
       * <code>int32 airBag = 21;</code>
       * @param value The airBag to set.
       * @return This builder for chaining.
       */
      public Builder setAirBag(int value) {
        
        airBag_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 安全气囊状态
       * </pre>
       *
       * <code>int32 airBag = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearAirBag() {
        bitField0_ = (bitField0_ & ~0x00100000);
        airBag_ = 0;
        onChanged();
        return this;
      }

      private int fuelGauge_ ;
      /**
       * <pre>
       * 剩余油量
       * </pre>
       *
       * <code>int32 fuelGauge = 22;</code>
       * @return The fuelGauge.
       */
      @java.lang.Override
      public int getFuelGauge() {
        return fuelGauge_;
      }
      /**
       * <pre>
       * 剩余油量
       * </pre>
       *
       * <code>int32 fuelGauge = 22;</code>
       * @param value The fuelGauge to set.
       * @return This builder for chaining.
       */
      public Builder setFuelGauge(int value) {
        
        fuelGauge_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 剩余油量
       * </pre>
       *
       * <code>int32 fuelGauge = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearFuelGauge() {
        bitField0_ = (bitField0_ & ~0x00200000);
        fuelGauge_ = 0;
        onChanged();
        return this;
      }

      private int soc_ ;
      /**
       * <pre>
       * 剩余电池电量
       * </pre>
       *
       * <code>int32 soc = 23;</code>
       * @return The soc.
       */
      @java.lang.Override
      public int getSoc() {
        return soc_;
      }
      /**
       * <pre>
       * 剩余电池电量
       * </pre>
       *
       * <code>int32 soc = 23;</code>
       * @param value The soc to set.
       * @return This builder for chaining.
       */
      public Builder setSoc(int value) {
        
        soc_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 剩余电池电量
       * </pre>
       *
       * <code>int32 soc = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoc() {
        bitField0_ = (bitField0_ & ~0x00400000);
        soc_ = 0;
        onChanged();
        return this;
      }

      private int epb_ ;
      /**
       * <pre>
       * 电子手刹状态
       * </pre>
       *
       * <code>int32 epb = 24;</code>
       * @return The epb.
       */
      @java.lang.Override
      public int getEpb() {
        return epb_;
      }
      /**
       * <pre>
       * 电子手刹状态
       * </pre>
       *
       * <code>int32 epb = 24;</code>
       * @param value The epb to set.
       * @return This builder for chaining.
       */
      public Builder setEpb(int value) {
        
        epb_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 电子手刹状态
       * </pre>
       *
       * <code>int32 epb = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearEpb() {
        bitField0_ = (bitField0_ & ~0x00800000);
        epb_ = 0;
        onChanged();
        return this;
      }

      private double wheelSpeedFL_ ;
      /**
       * <pre>
       * 前左轮速
       * </pre>
       *
       * <code>double wheelSpeedFL = 25;</code>
       * @return The wheelSpeedFL.
       */
      @java.lang.Override
      public double getWheelSpeedFL() {
        return wheelSpeedFL_;
      }
      /**
       * <pre>
       * 前左轮速
       * </pre>
       *
       * <code>double wheelSpeedFL = 25;</code>
       * @param value The wheelSpeedFL to set.
       * @return This builder for chaining.
       */
      public Builder setWheelSpeedFL(double value) {
        
        wheelSpeedFL_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 前左轮速
       * </pre>
       *
       * <code>double wheelSpeedFL = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearWheelSpeedFL() {
        bitField0_ = (bitField0_ & ~0x01000000);
        wheelSpeedFL_ = 0D;
        onChanged();
        return this;
      }

      private double wheelSpeedFR_ ;
      /**
       * <pre>
       * 前右轮速
       * </pre>
       *
       * <code>double wheelSpeedFR = 26;</code>
       * @return The wheelSpeedFR.
       */
      @java.lang.Override
      public double getWheelSpeedFR() {
        return wheelSpeedFR_;
      }
      /**
       * <pre>
       * 前右轮速
       * </pre>
       *
       * <code>double wheelSpeedFR = 26;</code>
       * @param value The wheelSpeedFR to set.
       * @return This builder for chaining.
       */
      public Builder setWheelSpeedFR(double value) {
        
        wheelSpeedFR_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 前右轮速
       * </pre>
       *
       * <code>double wheelSpeedFR = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearWheelSpeedFR() {
        bitField0_ = (bitField0_ & ~0x02000000);
        wheelSpeedFR_ = 0D;
        onChanged();
        return this;
      }

      private double wheelSpeedRL_ ;
      /**
       * <pre>
       * 后左轮速
       * </pre>
       *
       * <code>double wheelSpeedRL = 27;</code>
       * @return The wheelSpeedRL.
       */
      @java.lang.Override
      public double getWheelSpeedRL() {
        return wheelSpeedRL_;
      }
      /**
       * <pre>
       * 后左轮速
       * </pre>
       *
       * <code>double wheelSpeedRL = 27;</code>
       * @param value The wheelSpeedRL to set.
       * @return This builder for chaining.
       */
      public Builder setWheelSpeedRL(double value) {
        
        wheelSpeedRL_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 后左轮速
       * </pre>
       *
       * <code>double wheelSpeedRL = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearWheelSpeedRL() {
        bitField0_ = (bitField0_ & ~0x04000000);
        wheelSpeedRL_ = 0D;
        onChanged();
        return this;
      }

      private double wheelSpeedRR_ ;
      /**
       * <pre>
       * 后右轮速
       * </pre>
       *
       * <code>double wheelSpeedRR = 28;</code>
       * @return The wheelSpeedRR.
       */
      @java.lang.Override
      public double getWheelSpeedRR() {
        return wheelSpeedRR_;
      }
      /**
       * <pre>
       * 后右轮速
       * </pre>
       *
       * <code>double wheelSpeedRR = 28;</code>
       * @param value The wheelSpeedRR to set.
       * @return This builder for chaining.
       */
      public Builder setWheelSpeedRR(double value) {
        
        wheelSpeedRR_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 后右轮速
       * </pre>
       *
       * <code>double wheelSpeedRR = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearWheelSpeedRR() {
        bitField0_ = (bitField0_ & ~0x08000000);
        wheelSpeedRR_ = 0D;
        onChanged();
        return this;
      }

      private double motorSpeed_ ;
      /**
       * <pre>
       * 电机转速
       * </pre>
       *
       * <code>double motorSpeed = 29;</code>
       * @return The motorSpeed.
       */
      @java.lang.Override
      public double getMotorSpeed() {
        return motorSpeed_;
      }
      /**
       * <pre>
       * 电机转速
       * </pre>
       *
       * <code>double motorSpeed = 29;</code>
       * @param value The motorSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setMotorSpeed(double value) {
        
        motorSpeed_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 电机转速
       * </pre>
       *
       * <code>double motorSpeed = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotorSpeed() {
        bitField0_ = (bitField0_ & ~0x10000000);
        motorSpeed_ = 0D;
        onChanged();
        return this;
      }

      private double motorTorque_ ;
      /**
       * <pre>
       * 电机扭矩
       * </pre>
       *
       * <code>double motorTorque = 30;</code>
       * @return The motorTorque.
       */
      @java.lang.Override
      public double getMotorTorque() {
        return motorTorque_;
      }
      /**
       * <pre>
       * 电机扭矩
       * </pre>
       *
       * <code>double motorTorque = 30;</code>
       * @param value The motorTorque to set.
       * @return This builder for chaining.
       */
      public Builder setMotorTorque(double value) {
        
        motorTorque_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 电机扭矩
       * </pre>
       *
       * <code>double motorTorque = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearMotorTorque() {
        bitField0_ = (bitField0_ & ~0x20000000);
        motorTorque_ = 0D;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ext_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * G524 二进制数据，数据结构参考 planning
       * </pre>
       *
       * <code>bytes ext = 31;</code>
       * @return The ext.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExt() {
        return ext_;
      }
      /**
       * <pre>
       * G524 二进制数据，数据结构参考 planning
       * </pre>
       *
       * <code>bytes ext = 31;</code>
       * @param value The ext to set.
       * @return This builder for chaining.
       */
      public Builder setExt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ext_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * G524 二进制数据，数据结构参考 planning
       * </pre>
       *
       * <code>bytes ext = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearExt() {
        bitField0_ = (bitField0_ & ~0x40000000);
        ext_ = getDefaultInstance().getExt();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:proto.VehData)
    }

    // @@protoc_insertion_point(class_scope:proto.VehData)
    private static final com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData();
    }

    public static com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<VehData>
        PARSER = new com.google.protobuf.AbstractParser<VehData>() {
      @java.lang.Override
      public VehData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<VehData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<VehData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.les.its.scm.busdata.himalaya.proto.BsmDTO.VehData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_proto_VehData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_proto_VehData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tbsm.proto\022\005proto\"\334\004\n\007VehData\022\r\n\005vehId\030" +
      "\001 \001(\t\022\017\n\007plateNo\030\002 \001(\t\022\021\n\tlongitude\030\003 \001(" +
      "\001\022\020\n\010latitude\030\004 \001(\001\022\021\n\televation\030\005 \001(\001\022\025" +
      "\n\rtimestampGNSS\030\006 \001(\003\022\024\n\014velocityGNSS\030\007 " +
      "\001(\001\022\017\n\007heading\030\010 \001(\003\022\016\n\006tapPos\030\t \001(\005\022\025\n\r" +
      "steeringAngle\030\n \001(\005\022\r\n\005speed\030\013 \001(\001\022\027\n\017ac" +
      "celerationLon\030\014 \001(\001\022\027\n\017accelerationLat\030\r" +
      " \001(\001\022\027\n\017accelerationVer\030\016 \001(\001\022\017\n\007yawRate" +
      "\030\017 \001(\001\022\020\n\010accelPos\030\020 \001(\001\022\021\n\tbrakeFlag\030\021 " +
      "\001(\005\022\020\n\010brakePos\030\022 \001(\001\022\021\n\tdriveMode\030\023 \001(\005" +
      "\022\024\n\014tirePressure\030\024 \003(\005\022\016\n\006airBag\030\025 \001(\005\022\021" +
      "\n\tfuelGauge\030\026 \001(\005\022\013\n\003soc\030\027 \001(\005\022\013\n\003epb\030\030 " +
      "\001(\005\022\024\n\014wheelSpeedFL\030\031 \001(\001\022\024\n\014wheelSpeedF" +
      "R\030\032 \001(\001\022\024\n\014wheelSpeedRL\030\033 \001(\001\022\024\n\014wheelSp" +
      "eedRR\030\034 \001(\001\022\022\n\nmotorSpeed\030\035 \001(\001\022\023\n\013motor" +
      "Torque\030\036 \001(\001\022\013\n\003ext\030\037 \001(\014BF\n&com.les.its" +
      ".scm.busdata.himalaya.protoB\006BsmDTOP\000Z\022c" +
      "om.tyjt/proto/vehb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_proto_VehData_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_proto_VehData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_proto_VehData_descriptor,
        new java.lang.String[] { "VehId", "PlateNo", "Longitude", "Latitude", "Elevation", "TimestampGNSS", "VelocityGNSS", "Heading", "TapPos", "SteeringAngle", "Speed", "AccelerationLon", "AccelerationLat", "AccelerationVer", "YawRate", "AccelPos", "BrakeFlag", "BrakePos", "DriveMode", "TirePressure", "AirBag", "FuelGauge", "Soc", "Epb", "WheelSpeedFL", "WheelSpeedFR", "WheelSpeedRL", "WheelSpeedRR", "MotorSpeed", "MotorTorque", "Ext", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
