package com.les.its.scm.busdata;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Project: nacos-server-parent
 * @Package: com.les.its.scm.web.task
 * @Author： mujian
 * @Create： 2023/3/14 10:52
 * @Description： TODO
 * @History: modify
 */
@EnableScheduling
@Component
@Slf4j
public class GpsSendScheduling {

    @Autowired
    GpsCache gpsCache;

    @Autowired
    BusGPSService busGPSService;

    /**
     *
     */
    @Scheduled(cron = "0/1 * * * * ?")
    @Async
    public void crossingFiveMinVolumeToRedis() {
        try {
            List<GpsData> gpsDataList = gpsCache.getGpsDataList();
            if(!CollectionUtils.isEmpty(gpsDataList)){
                for(GpsData gpsData : gpsDataList){
                    busGPSService.sendBusGPS(gpsData);
                    busGPSService.sendBusGPSToSSC(gpsData);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }






}
