2025-09-17 08:33:55.657  INFO 30916 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Starting BusDataZzApplication using Java 1.8.0_442 on king-work with PID 30916 (C:\Users\<USER>\Desktop\bus-data-zz\target\classes started by LES in C:\Users\<USER>\Desktop\bus-data-zz)
2025-09-17 08:33:55.660 DEBUG 30916 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Running with Spring Boot v2.6.15, Spring v5.3.27
2025-09-17 08:33:55.661  INFO 30916 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : No active profile set, falling back to 1 default profile: "default"
2025-09-17 08:33:57.376  INFO 30916 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17 08:33:57.420  INFO 30916 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17 08:33:58.777  INFO 30916 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 18111 (http)
2025-09-17 08:33:58.806  INFO 30916 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17 08:33:58.806  INFO 30916 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-09-17 08:33:58.929  INFO 30916 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17 08:33:58.929  INFO 30916 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3206 ms
2025-09-17 08:33:59.170  INFO 30916 --- [main] com.les.its.scm.busdata.BusMqttMessage   : username:admin password:admin hostUrl:tcp://***************:1883 clientId :cf07571f3ca1868dfa112c7ec883adbd 
2025-09-17 08:34:00.449  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17 08:34:00.450  INFO 30916 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17 08:34:00.450  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17 08:34:00.450  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:busMqttMessage.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-09-17 08:34:00.451  INFO 30916 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.mqttInputChannel' has 1 subscriber(s).
2025-09-17 08:34:00.451  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'busMqttMessage.handler.serviceActivator'
2025-09-17 08:34:00.451  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:busMqttMessage.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-09-17 08:34:00.451  INFO 30916 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.mqttOutboundChannel' has 1 subscriber(s).
2025-09-17 08:34:00.451  INFO 30916 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'busMqttMessage.mqttOutbound.serviceActivator'
2025-09-17 08:34:02.016  INFO 30916 --- [main] .m.i.MqttPahoMessageDrivenChannelAdapter : started bean 'inbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.inbound()'
2025-09-17 08:34:02.058  INFO 30916 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 18111 (http) with context path ''
2025-09-17 08:34:02.074 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:02.081  INFO 30916 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Started BusDataZzApplication in 8.835 seconds (JVM running for 17.618)
2025-09-17 08:34:02.517 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:02.626 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:07.087 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:07.088 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:07.089 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:12.079 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:12.080 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:12.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:17.075 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:17.076 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:17.077 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:22.080 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:22.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:22.082 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:27.088 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:27.089 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:27.090 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:32.077 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:32.078 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:32.079 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:37.086 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:37.087 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:37.089 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:42.079 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:42.080 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:42.080 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:47.081 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:47.082 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:47.083 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:52.079 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:52.080 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:52.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:57.088 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:34:57.088 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:34:57.089 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:02.080 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:02.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:02.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:07.080 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:07.081 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:07.082 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:12.088 DEBUG 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:12.088 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 发送传感器数据异常: topic=idse/tps-VehicleData-bsm/#, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0


org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) [classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:12.088 ERROR 30916 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据失败

java.lang.RuntimeException: MQTT发送失败
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:89) ~[classes/:na]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendPeriodicTestData(MqttSenderService.java:47) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_442]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84) [spring-context-5.3.27.jar:5.3.27]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) [spring-context-5.3.27.jar:5.3.27]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) [na:1.8.0_442]
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180) [na:1.8.0_442]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294) [na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_442]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]
Caused by: org.springframework.messaging.MessageHandlingException: error occurred in message handler [bean 'mqttOutbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.mqttOutbound()']; nested exception is java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.springframework.integration.support.utils.IntegrationUtils.wrapInHandlingExceptionIfNecessary(IntegrationUtils.java:191) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:64) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at com.les.its.scm.busdata.sender.MqttSenderService.sendSensorData(MqttSenderService.java:76) ~[classes/:na]
	... 14 common frames omitted
Caused by: java.lang.IllegalArgumentException: The topic name MUST NOT contain any wildcard characters (#+)
	at org.eclipse.paho.client.mqttv3.MqttTopic.validate(MqttTopic.java:226) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1366) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.publish(MqttAsyncClient.java:1348) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler.publish(MqttPahoMessageHandler.java:208) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.outbound.AbstractMqttMessageHandler.handleMessageInternal(AbstractMqttMessageHandler.java:340) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	... 24 common frames omitted

2025-09-17 08:35:12.104  INFO 30916 --- [SpringApplicationShutdownHook] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped bean 'inbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.inbound()'
2025-09-17 08:35:12.107  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17 08:35:12.107  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17 08:35:12.107  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:busMqttMessage.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.mqttInputChannel' has 0 subscriber(s).
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'busMqttMessage.handler.serviceActivator'
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:busMqttMessage.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.mqttOutboundChannel' has 0 subscriber(s).
2025-09-17 08:35:12.108  INFO 30916 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'busMqttMessage.mqttOutbound.serviceActivator'
2025-09-17 08:35:20.129  INFO 34748 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Starting BusDataZzApplication using Java 1.8.0_442 on king-work with PID 34748 (C:\Users\<USER>\Desktop\bus-data-zz\target\classes started by LES in C:\Users\<USER>\Desktop\bus-data-zz)
2025-09-17 08:35:20.131 DEBUG 34748 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Running with Spring Boot v2.6.15, Spring v5.3.27
2025-09-17 08:35:20.132  INFO 34748 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : No active profile set, falling back to 1 default profile: "default"
2025-09-17 08:35:21.023  INFO 34748 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17 08:35:21.064  INFO 34748 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17 08:35:21.738  INFO 34748 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 18111 (http)
2025-09-17 08:35:21.758  INFO 34748 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-17 08:35:21.759  INFO 34748 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-09-17 08:35:21.849  INFO 34748 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-17 08:35:21.849  INFO 34748 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1659 ms
2025-09-17 08:35:21.924  INFO 34748 --- [main] com.les.its.scm.busdata.BusMqttMessage   : username:admin password:admin hostUrl:tcp://***************:1883 clientId :0c6bcd477eeffb572e60442a783b9ab5 
2025-09-17 08:35:22.907  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17 08:35:22.907  INFO 34748 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17 08:35:22.907  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:busMqttMessage.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.mqttInputChannel' has 1 subscriber(s).
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'busMqttMessage.handler.serviceActivator'
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:busMqttMessage.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.mqttOutboundChannel' has 1 subscriber(s).
2025-09-17 08:35:22.908  INFO 34748 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'busMqttMessage.mqttOutbound.serviceActivator'
2025-09-17 08:35:23.292  INFO 34748 --- [main] .m.i.MqttPahoMessageDrivenChannelAdapter : started bean 'inbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.inbound()'
2025-09-17 08:35:23.336  INFO 34748 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 18111 (http) with context path ''
2025-09-17 08:35:23.348 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:23.352  INFO 34748 --- [main] c.l.i.scm.busdata.BusDataZzApplication   : Started BusDataZzApplication in 3.981 seconds (JVM running for 10.276)
2025-09-17 08:35:23.822 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:23.831 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:23.832 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:23.824  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:23.868 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:28.352 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:28.356 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:28.356  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:28.357 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:28.357 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:28.357 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:33.358 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:33.362 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:33.362  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:33.362 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:33.362 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:33.363 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:38.353 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:38.357 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:38.357  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:38.357 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:38.358 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:38.357 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:43.348 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:43.351 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:43.352 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:43.352  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:43.352 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:43.352 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:48.363 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:48.366 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:48.367  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:48.367 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:48.367 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:48.368 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:53.353 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:53.356 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:53.357  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:53.357 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:53.357 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:35:53.357 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:58.349 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:35:58.353 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:35:58.353 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:58.353  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:35:58.354 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:35:58.354 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:03.356 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:03.360 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:03.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:03.361  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:03.361 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:03.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:08.354 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:08.357 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:08.358  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:08.358 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:08.358 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:08.359 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:13.357 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:13.360 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:13.361  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:13.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:13.361 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:13.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:18.350 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:18.354 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:18.355  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:18.355 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:18.355 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:18.355 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:23.364 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:23.367 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:23.368  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:23.368 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:23.368 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:23.368 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:28.351 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:28.354 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:28.354  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:28.354 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:28.354 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:28.355 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:33.348 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:33.351 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:33.351  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:33.351 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:33.352 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:33.352 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:38.358 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:38.361 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:38.362 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:38.362  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:38.362 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:38.362 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:43.357 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:43.360 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:43.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:43.361  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:43.361 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:43.361 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:48.353 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:48.358 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:48.358 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:48.359 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:48.361  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:48.361 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:53.360 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:53.364 DEBUG 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : 数据不是 base64 编码格式
2025-09-17 08:36:53.364 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:204) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:53.364 ERROR 34748 --- [MQTT Call: 0c6bcd477eeffb572e60442a783b9ab5_inbound] com.les.its.scm.busdata.BusMqttMessage   : Protobuf 数据解析失败，数据长度: 46

com.google.protobuf.InvalidProtocolBufferException: Protocol message contained an invalid tag (zero).
	at com.google.protobuf.InvalidProtocolBufferException.invalidTag(InvalidProtocolBufferException.java:133) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readTag(CodedInputStream.java:633) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:490) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:604) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFrom(UnknownFieldSet.java:304) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.CodedInputStream$ArrayDecoder.readGroup(CodedInputStream.java:837) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.UnknownFieldSet$Builder.mergeFieldFrom(UnknownFieldSet.java:518) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.GeneratedMessageV3$Builder.parseUnknownField(GeneratedMessageV3.java:863) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$Builder.mergeFrom(BsmDTO.java:2045) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3620) ~[classes/:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData$1.parsePartialFrom(BsmDTO.java:3612) ~[classes/:na]
	at com.google.protobuf.AbstractParser.parsePartialFrom(AbstractParser.java:158) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:191) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:203) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:208) ~[protobuf-java-3.21.12.jar:na]
	at com.google.protobuf.AbstractParser.parseFrom(AbstractParser.java:48) ~[protobuf-java-3.21.12.jar:na]
	at com.les.its.scm.busdata.himalaya.proto.BsmDTO$VehData.parseFrom(BsmDTO.java:1439) ~[classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.processProtoData(BusMqttMessage.java:266) [classes/:na]
	at com.les.its.scm.busdata.BusMqttMessage.lambda$handler$0(BusMqttMessage.java:208) [classes/:na]
	at org.springframework.integration.handler.ReplyProducingMessageHandlerWrapper.handleRequestMessage(ReplyProducingMessageHandlerWrapper.java:59) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractReplyProducingMessageHandler.handleMessageInternal(AbstractReplyProducingMessageHandler.java:136) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109) ~[spring-messaging-5.3.27.jar:5.3.27]
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215) ~[spring-integration-core-5.5.18.jar:5.5.18]
	at org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter.messageArrived(MqttPahoMessageDrivenChannelAdapter.java:398) ~[spring-integration-mqtt-5.5.18.jar:5.5.18]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.deliverMessage(CommsCallback.java:519) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.handleMessage(CommsCallback.java:417) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at org.eclipse.paho.client.mqttv3.internal.CommsCallback.run(CommsCallback.java:214) ~[org.eclipse.paho.client.mqttv3-1.2.5.jar:na]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_442]

2025-09-17 08:36:53.364  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:53.365 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:58.354 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送测试数据...
2025-09-17 08:36:58.356  INFO 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 成功发送传感器数据到MQTT: topic=idse/tps-VehicleData-bsm/uuu, sensorData=vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0
, payloadSize=42
2025-09-17 08:36:58.356 DEBUG 34748 --- [scheduling-1] c.l.i.s.b.sender.MqttSenderService       : 定时发送完成，vehId: "999"
plateNo: "\350\213\217A8888"
longitude: 66.0
latitude: 111.0
speed: 40.0

2025-09-17 08:36:59.623  INFO 34748 --- [SpringApplicationShutdownHook] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped bean 'inbound'; defined in: 'class path resource [com/les/its/scm/busdata/BusMqttMessage.class]'; from source: 'com.les.its.scm.busdata.BusMqttMessage.inbound()'
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:busMqttMessage.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.mqttInputChannel' has 0 subscriber(s).
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'busMqttMessage.handler.serviceActivator'
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:busMqttMessage.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.mqttOutboundChannel' has 0 subscriber(s).
2025-09-17 08:36:59.626  INFO 34748 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'busMqttMessage.mqttOutbound.serviceActivator'
