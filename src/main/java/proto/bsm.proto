syntax = "proto3";

package proto;

option go_package = "com.tyjt/proto/veh";
//option java_package = "com.tyjt.himalaya.proto";
option java_package = "com.les.its.scm.busdata.himalaya.proto";
option java_outer_classname = "BsmDTO";
option java_multiple_files = false;

message VehData {
  string vehId = 1;                   // 车辆id
  string plateNo = 2;                 // 车牌
  double longitude = 3;              // 经度
  double latitude = 4;               // 纬度
  double elevation = 5;              // 高程
  int64 timestampGNSS = 6;           //GNSS 时间戳
  double velocityGNSS = 7;           //GNSS 速度
  int64 heading = 8;                 //航向角
  int32 tapPos = 9;                  //  档位
  int32 steeringAngle = 10;          //  方向盘转角
  double speed = 11;                 //  当前车速
  double accelerationLon = 12;       //  纵向加速度
  double accelerationLat = 13;       //  横向加速度
  double accelerationVer = 14;       //  垂向加速度
  double yawRate = 15;               //  横摆角速度
  double accelPos = 16;              // 油门开度
  int32 brakeFlag = 17;              //   制动踏板开关
  double brakePos = 18;              //  制动踏板开度
  int32 driveMode = 19;               // 车辆驾驶模式

  repeated int32 tirePressure = 20;   // 胎压
  int32 airBag = 21;                  // 安全气囊状态
  int32 fuelGauge = 22;               // 剩余油量
  int32 soc = 23;                     // 剩余电池电量
  int32 epb = 24;                     // 电子手刹状态

  double wheelSpeedFL = 25;           // 前左轮速
  double wheelSpeedFR = 26;           // 前右轮速
  double wheelSpeedRL = 27;           // 后左轮速
  double wheelSpeedRR = 28;           // 后右轮速
  double motorSpeed = 29;              // 电机转速
  double motorTorque = 30;             // 电机扭矩

  bytes ext = 31;                      // G524 二进制数据，数据结构参考 planning

}
