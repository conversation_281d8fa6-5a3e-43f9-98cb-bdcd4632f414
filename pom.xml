<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.myweb</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.6-SNAPSHOT</version>
    </parent>
    <groupId>com.les.its.scm</groupId>
    <artifactId>bus-data-zz</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>bus-data-zz</name>
    <description>bus-data-zz</description>
    <properties>
        <java.version>1.8</java.version>
        <protobuf.version>3.21.12</protobuf.version>
        <protoc.version>3.21.12</protoc.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.les.its.scm</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.les.its.scm</groupId>
            <artifactId>data-struct</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.47</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>org.bouncycastle</groupId>-->
<!--            <artifactId>bcpkix-jdk15on</artifactId>-->
<!--            <version>1.70</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.google.protobuf</groupId>-->
<!--            <artifactId>protobuf-java</artifactId>-->
<!--            <version>3.21.12</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.protobuf</groupId>-->
<!--            <artifactId>protobuf-java</artifactId>-->
<!--            <version>3.21.12</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.protobuf</groupId>-->
<!--            <artifactId>protobuf-java</artifactId>-->
<!--            <version>4.32.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.protobuf</groupId>-->
<!--            <artifactId>protobuf-java-util</artifactId>-->
<!--            <version>4.32.0</version>-->
<!--        </dependency>-->
    </dependencies>

    <build>

        <extensions>
            <!-- OS检测插件，用于设置${os.detected.classifier}等属性 -->
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.1</version>
            </extension>
        </extensions>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.os72</groupId>
                <artifactId>protoc-jar-maven-plugin</artifactId>
                <version>3.11.4</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <inputDirectories>
                                <include>src/main/java/proto</include>
                            </inputDirectories>
                            <outputTargets>
                                <outputTarget>
                                    <type>java</type>
                                    <outputDirectory>src/main/java</outputDirectory>
                                </outputTarget>
                            </outputTargets>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <configuration>
                    <!-- protoc编译器版本 -->
                    <protocArtifact>com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}</protocArtifact>
                    <!-- proto文件目录 -->
                    <protoSourceRoot>src/main/java/proto</protoSourceRoot>
                    <!-- 输出目录 -->
                    <outputDirectory>src/main/java</outputDirectory>
                    <!-- 清理输出目录 -->
                    <clearOutputDirectory>false</clearOutputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- OS检测插件（protobuf插件需要） -->
            <plugin>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>detect</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

</project>
