package com.les.its.scm.busdata;

import com.les.ads.common.util.DeepCloneUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


@Slf4j
@Component
public class GpsCache {

    private static final Lock BUS_GPS_LOCK = new ReentrantLock(true);
    private final Map<String, GpsData> gpsDataMap = new LinkedHashMap<>();

    public void addGpsData(GpsData gpsData) {
        BUS_GPS_LOCK.lock();
        try {
            gpsDataMap.put(gpsData.getImei(), gpsData);
        } finally {
            BUS_GPS_LOCK.unlock();
        }
    }

    public List<GpsData> getGpsDataList() {
        List<GpsData> copyList;
        BUS_GPS_LOCK.lock();
        try {
            copyList = DeepCloneUtil.mapperData(new ArrayList<>(gpsDataMap.values()), GpsData.class);
            gpsDataMap.clear();
        } finally {
            BUS_GPS_LOCK.unlock();
        }
        return copyList;
    }
}
