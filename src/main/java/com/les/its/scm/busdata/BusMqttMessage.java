package com.les.its.scm.busdata;

import com.alibaba.fastjson.JSONObject;
import com.les.ads.common.util.DateUtil;
import com.les.its.scm.busdata.himalaya.proto.BsmDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * @Project: signal-cabinet
 * @Package: com.les.cabinet
 * @Author： mujian
 * @Create： 2021/8/5 14:07
 * @Description： TODO
 * @History: modify
 */
@Slf4j
@Configuration
@IntegrationComponentScan
@Getter
@Setter
@ConditionalOnProperty(prefix = "mqtt", name = "serverURIs")
public class BusMqttMessage {

    public static final String OUTBOUND_CHANNEL = "mqttOutboundChannel";

    public static final String INPUT_CHANNEL = "mqttInputChannel";

    @Value("${mqtt.subTopics}")
    public String subTopics;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Value("${mqtt.serverURIs}")
    private String hostUrl;

    @Value("${mqtt.client.id}")
    private String clientId;

    @Value("${mqtt.topic}")
    private String defaultTopic;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    BusGPSService busGPSService;

    @Autowired
    GpsCache gpsCache;


    @PostConstruct
    public void init() {
        log.info("username:{} password:{} hostUrl:{} clientId :{} ",
                this.username, this.password, this.hostUrl, this.clientId, this.defaultTopic);
    }

    @Bean
    public MqttPahoClientFactory clientFactory() {

        final MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{hostUrl});
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        final DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        factory.setConnectionOptions(options);
        return factory;
    }

    @Bean(value = OUTBOUND_CHANNEL)
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    @Bean
    @ServiceActivator(inputChannel = OUTBOUND_CHANNEL)
    public MessageHandler mqttOutbound() {

        final MqttPahoMessageHandler handler = new MqttPahoMessageHandler(clientId, clientFactory());
        handler.setDefaultQos(1);
        handler.setDefaultRetained(false);
        handler.setDefaultTopic(defaultTopic);
        handler.setAsync(false);
        handler.setAsyncEvents(false);
        return handler;
    }

    /**
     * MQTT消息接收处理
     */
    //接收通道
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    //配置client,监听的topic

    @Bean
    public MessageProducer mqttInbound() {
        String[] topics = subTopics.split(",");
        log.info("订阅 MQTT 主题: {}", Arrays.toString(topics));

        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(
                        clientId + "_inbound", mqttClientFactory(), topics);
        adapter.setCompletionTimeout(5000);

        // 配置消息转换器以接收原始字节
        DefaultPahoMessageConverter converter = new DefaultPahoMessageConverter() {
            @Override
            protected Object mqttBytesToPayload(byte[] bytes) {
                // 返回原始字节数组而不是转换为字符串
                return bytes;
            }
        };
        adapter.setConverter(converter);
        adapter.setQos(1);
        adapter.setOutputChannel(mqttInputChannel());
        return adapter;
    }

    //通过通道获取数据
//    @Bean
//    @ServiceActivator(inputChannel = INPUT_CHANNEL)
//    public MessageHandler handler() {
//        return message -> {
//            try {
//                LocalDateTime now = LocalDateTime.now();
//                log.debug( message.getPayload().toString());
//                GpsData gpsData = new GpsData();
//                try {
//                    String data = message.getPayload().toString();
//                    JSONObject jsonObject = JSONObject.parseObject(data);
//                    gpsData.setNoTab(201);
//                    gpsData.setImei(jsonObject.getString("vehId"));
//                    gpsData.setLng(jsonObject.getDouble("longitude"));
//                    gpsData.setLat(jsonObject.getDouble("latitude"));
//                    gpsData.setSpeed(jsonObject.getDouble("speed").floatValue());
//                    gpsData.setBear(jsonObject.getInteger("heading").floatValue());
//                    gpsData.setGpsState(0);
//                    long second = jsonObject.getLong("timestampGNSS");
//
//                    LocalDateTime time = new Date(second).toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
//
//                    long differenceDateSeconds = DateUtil.differenceDateSeconds(time, now);
//                    if(differenceDateSeconds>30){
//                        return;
//                    }
//                    gpsData.setDate(time);
//                } catch (Exception e) {
//                    log.error(" message:{}", message.getPayload().toString());
//                }
//
//                gpsCache.addGpsData(gpsData);

    /// /                busGPSService.sendBusGPS(gpsData);
    /// /                busGPSService.sendBusGPSToSSC(gpsData);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//        };
//    }

    @ServiceActivator(inputChannel = INPUT_CHANNEL)
    public void handler(Message<byte[]> message) {
       {
            try {
                LocalDateTime now = LocalDateTime.now();

                // 处理字节数组形式的 protobuf 数据
                {
                    processProtoData(message.getPayload() , now);
                }

            } catch (Exception e) {
                log.error("处理 MQTT 消息异常: ", e);
            }
        };
    }

    private void processProtoData(byte[] data, LocalDateTime now) {
        try {
            if (data == null || data.length == 0) {
                log.warn("接收到空的 protobuf 数据");
                return;
            }

            // 解析 protobuf 数据
//                com.les.its.scm.busdata.himalaya.proto.BsmDTO bsmData =
//                        com.les.its.scm.busdata.himalaya.proto.BsmDTO.registerAllExtensions(payload);
            BsmDTO.VehData bsmData = BsmDTO.VehData.parseFrom(data);
            GpsData gpsData = new GpsData();
            gpsData.setNoTab(201);
            gpsData.setImei(bsmData.getVehId()); // 车辆ID
            gpsData.setLng(bsmData.getLongitude()); // 经度
            gpsData.setLat(bsmData.getLatitude()); // 纬度
            gpsData.setSpeed((float) bsmData.getSpeed()); // 速度
            gpsData.setBear((float) bsmData.getHeading()); // 航向角

            // 假设 gpsState 默认为 0
            gpsData.setGpsState(0);

            // 时间戳处理
            long timestamp = bsmData.getTimestampGNSS();
            LocalDateTime time = java.time.Instant.ofEpochMilli(timestamp)
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime();

            long differenceDateSeconds = DateUtil.differenceDateSeconds(time, now);
            if (differenceDateSeconds > 30) {
                return; // 数据过期，丢弃
            }
            gpsData.setDate(time);

            log.debug("GPS 数据: {}", JSONObject.toJSONString(gpsData));
            gpsCache.addGpsData(gpsData);

        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            log.error("Protobuf 数据解析失败，数据长度: {}", data.length, e);
        } catch (Exception e) {
            log.error("处理 protobuf 数据时出错", e);
        }
    }

}
